import { useWorkspaceContext } from '@/contexts/workspaceContext';
import RunAPIDrawer from '@/pages/editor/query/RunAPIDrawer';
import { QueryCommand } from '@/pages/editor/result/CommandExecutor';
import { ApiType } from '@/pages/workgroup/tab/restPP/type';
import { GsqlQueryMeta, QueryMetaLogic, QueryParam } from '@tigergraph/tools-models';
import { useMemo } from 'react';

export interface RunQueryDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onRun: (cmd: QueryCommand) => void;
  graphName: string;
  query: GsqlQueryMeta;
}

export default function RunQueryDrawer(props: RunQueryDrawerProps) {
  const { isOpen, onClose, onRun, graphName, query } = props;
  const { currentWorkspace } = useWorkspaceContext();

  const queryParams: QueryParam[] = useMemo(() => {
    const params = query.endpoint?.query?.[graphName]?.[query.name]?.['GET/POST']?.parameters || {};
    return QueryMetaLogic.convertGSQLParameters(params);
  }, [query, graphName]);

  return (
    <RunAPIDrawer
      isOpen={isOpen}
      onClose={onClose}
      onRun={onRun}
      wp={currentWorkspace!}
      graphName={graphName}
      path={`/restpp/query/${graphName}/${query.name}`}
      method={query.installed ? ApiType.POST : ApiType.GET}
      parameters={queryParams}
      query={query}
      type="query"
    />
  );
}
