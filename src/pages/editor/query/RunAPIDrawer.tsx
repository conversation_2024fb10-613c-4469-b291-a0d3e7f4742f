import { useState, useEffect, useCallback, useMemo } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import JSONbigint from 'json-bigint';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Drawer, DrawerBody, DrawerAction, DrawerHeader } from '@/components/Drawer';
import useEditorTheme from '@/pages/editor/useEditorTheme';
import { showToast, StyledToast } from '@/components/styledToasterContainer';
import { GsqlQueryMeta, QueryParam, QueryParamType } from '@tigergraph/tools-models';
import { MdPlayArrow } from 'react-icons/md';
import { QueryCommand } from '@/pages/editor/result/CommandExecutor';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { KIND } from 'baseui/toast';
import QueryParamForm from './params/QueryParamForm';
import QueryConfig from '@/pages/editor/header/RunConfig';
import {
  buildParamsForInstalledMode,
  buildParamsForInterpretedMode,
  getQueryDefalutPayloadByParams,
  ParamError,
  ParamErrors,
} from '@/utils/queryParam';
import { useSchema } from '@/utils/useSchema';
import { CreateToken } from './CreateToken';
import { Input } from '@tigergraph/app-ui-lib/input';
import { CopyIcon } from '@/pages/home/<USER>';
import TooltipLabel from '@/components/TooltipLabel';
import SnippetsCopier from '@/pages/editor/query/SnippetsCopier';
import HttpMethodBadge from '@/components/HttpMethodBadge';
import { ApiType } from '@/pages/workgroup/tab/restPP/type';
import { Tabs, Tab } from '@/components/Tab';
import ReactCodeMirror from '@uiw/react-codemirror';
import { StreamLanguage } from '@codemirror/language';
import { json } from '@codemirror/legacy-modes/mode/javascript';
import { expand } from 'inline-style-expand-shorthand';
import { WorkspaceT } from '@/pages/workgroup/type';

export interface RunAPIDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onRun: (cmd: QueryCommand) => void;
  type: 'query' | 'restpp endpoints';
  wp: WorkspaceT;
  graphName: string;
  // url path which begins at '/restpp'
  path: string;
  method: ApiType;
  pathParameters?: QueryParam[];
  parameters: QueryParam[];
  query?: GsqlQueryMeta;
}

export default function RunAPIDrawer(props: RunAPIDrawerProps) {
  const { isOpen, onClose, onRun, wp, graphName, query, path, method, pathParameters = [], parameters, type } = props;

  const [css, theme] = useStyletron();
  const editorTheme = useEditorTheme({ background: theme.colors['input.background'] });
  const { data: schema } = useSchema(wp, graphName, isOpen);

  // Tab state
  const [activeTab, setActiveTab] = useState('parameter');

  const [copyContent, setCopyContent] = useState('');
  useEffect(() => {
    if (copyContent) {
      navigator.clipboard.writeText(copyContent);
      showToast({
        kind: 'positive',
        message: 'Copied to clipboard',
      });
    }
  }, [copyContent]);

  const [payload, setPayload] = useState<Record<any, any>>({});
  const [pathPayload, setPathPayload] = useState<Record<string, string>>({});
  const [paramErrors, setParamErrors] = useState<ParamErrors>({});
  useEffect(() => {
    setPathPayload(getQueryDefalutPayloadByParams(pathParameters));
  }, [pathParameters]);
  useEffect(() => {
    setPayload(getQueryDefalutPayloadByParams(parameters));
  }, [parameters]);

  const url = useMemo(() => {
    let modifiedPath = path.replace(/{([^}]+)}/g, function (match, content) {
      const value = pathPayload[content];
      return value;
    });

    if (method === ApiType.GET) {
      modifiedPath += '?' + buildParamsForInterpretedMode(graphName, parameters, payload);
    }

    return `https://${wp.nginx_host}${modifiedPath}`;
  }, [path, method, wp.nginx_host, pathPayload, graphName, parameters, payload]);

  const { memoryLimit, timeLimit, profile } = useEditorContext();

  const validateSimpleTypeInput = (paramType: QueryParamType, value: any): string => {
    if (paramType.type === 'INT') {
      if (!/^-?\d+$/.test(value)) {
        return 'Please input a valid INT.';
      }
    }

    if (paramType.type === 'UINT') {
      if (!/^\d+$/.test(value)) {
        return 'Please input a valid UINT.';
      }
    }

    if (paramType.type === 'FLOAT' || paramType.type === 'DOUBLE') {
      if (isNaN(Number(value)) || value === '') {
        return `Please input a valid ${paramType.type}.`;
      }
    }

    if (paramType.type === 'VERTEX') {
      if (!value?.id) {
        return 'Please input a valid vertex ID.';
      }
    }

    if (paramType.type === 'DATETIME') {
      if ((isNaN(Number(value)) && isNaN(Date.parse(value))) || value === '') {
        return 'Please input a valid DATETIME.';
      }
    }

    return '';
  };

  const validateInput = useCallback((param: QueryParam, value: any): { error: ParamError; hasError: boolean } => {
    const type = param.paramType.type;
    let hasError = false;
    let error: ParamError = '';
    if (type === 'LIST') {
      error = value.map((item: any) => validateSimpleTypeInput(param.paramType.elementType, item));
      hasError = (error as string[]).some((err: string) => !!err);
    } else if (type === 'MAP') {
      error = value.map(({ key, value }: { key: any; value: any }) => ({
        key: validateSimpleTypeInput(param.paramType.keyType, key),
        value: validateSimpleTypeInput(param.paramType.valueType, value),
      }));
      hasError = (error as { key: string; value: string }[]).some(({ key, value }) => !!key || !!value);
    } else {
      error = validateSimpleTypeInput(param.paramType, value);
      hasError = !!error;
    }

    return { error, hasError };
  }, []);

  const validateAllInputs = useCallback(
    (queryPayload: Record<string, any>) => {
      const errors: ParamErrors = {};
      let hasError = false;
      for (const param of parameters) {
        const value = queryPayload[param.paramName];
        const { error, hasError: _hasError } = validateInput(param, value);
        errors[param.paramName] = error;
        hasError = hasError || _hasError;
      }
      setParamErrors(errors);

      return hasError;
    },
    [parameters, validateInput]
  );

  const onParamChange = useCallback(
    (param: QueryParam, value: any) => {
      setPayload((prev) => ({ ...prev, [param.paramName]: value }));

      const { error } = validateInput(param, value);
      setParamErrors((prev) => ({ ...prev, [param.paramName]: error }));
    },
    [validateInput]
  );
  const onPathParamChange = useCallback((param: QueryParam, value: any) => {
    setPathPayload((prev) => ({ ...prev, [param.paramName]: value }));
  }, []);

  const onRunClick = () => {
    if (validateAllInputs(payload)) return;

    // onRun(
    //   new QueryCommand(
    //     wp.workspace_id,
    //     graphName,
    //     query,
    //     queryPayload,
    //     {
    //       memoryLimit,
    //       timeLimit,
    //       profile,
    //     },
    //     {
    //       nginx_host: wp.nginx_host,
    //       tg_version: wp.tg_version,
    //     }
    //   )
    // );
    onClose();
  };

  const [isCreateSecretOpen, setIsCreateSecretOpen] = useState(false);
  const [token, setToken] = useState('');

  return (
    <Drawer isOpen={isOpen} onClose={onClose} size="auto">
      <DrawerHeader>
        <div className="flex items-center gap-[8px]">
          <MdPlayArrow size={24} />
          <div className="w-[500px] truncate">
            {`Run ${type === 'query' ? 'Query' : 'API'}`} : {type === 'query' ? query?.name : path}
          </div>
        </div>
      </DrawerHeader>
      <DrawerBody $style={{ ...expand({ padding: '0' }) }}>
        <div className="flex flex-col gap-[12px] h-full">
          {/* <div className={css({ fontSize: '16px', fontWeight: 700 })}>{query.name}</div> */}

          <Tabs activeKey={activeTab} onChange={({ activeKey }) => setActiveTab(activeKey as string)}>
            <Tab
              title="Parameter"
              key="parameter"
              overrides={{
                TabPanel: {
                  style: {
                    height: '100%',
                    ...expand({ padding: '12px 16px' }),
                  },
                },
              }}
            >
              {type === 'restpp endpoints' && pathParameters.length > 0 && (
                <>
                  <div className={css({ color: theme.colors['text.primary'], fontWeight: 600, marginBottom: '8px' })}>
                    Path Parameters
                  </div>
                  <QueryParamForm
                    queryParams={pathParameters}
                    queryPayload={pathPayload}
                    onQueryParamChange={onPathParamChange}
                    graphName={graphName}
                    paramErrors={{}}
                  />
                </>
              )}

              <>
                {type === 'restpp endpoints' && (
                  <div className={css({ color: theme.colors['text.primary'], fontWeight: 600, marginBottom: '8px' })}>
                    API Parameters
                  </div>
                )}
                <QueryParamForm
                  queryParams={parameters}
                  queryPayload={payload}
                  onQueryParamChange={onParamChange}
                  graphName={graphName}
                  paramErrors={paramErrors}
                />
              </>

              {type === 'query' && !query?.installed && (
                <div className={css({ marginTop: '16px' })}>
                  <StyledToast
                    kind={KIND.info}
                    closeable={false}
                    message="The query is not installed and will be run in interpreted mode. Interpreted queries have limited feature support and may result in slower performance. For optimal execution, please install the query first."
                  />
                </div>
              )}
            </Tab>

            <Tab
              title="Body"
              key="body"
              overrides={{
                TabPanel: {
                  style: {
                    height: '100%',
                    ...expand({ padding: '12px 16px' }),
                  },
                },
              }}
            >
              <div className="flex flex-col gap-[8px]">
                <div className="flex justify-between items-center">
                  <div
                    className={css({
                      fontSize: '16x',
                      lineHeight: '24px',
                      color: theme.colors['input.text'],
                      fontWeight: 500,
                    })}
                  >
                    JSON Payload
                  </div>
                  <Button
                    size="compact"
                    kind="text"
                    shape="square"
                    onClick={() => {
                      setCopyContent(JSONbigint.stringify(buildParamsForInstalledMode(parameters, payload), null, 2));
                    }}
                  >
                    <CopyIcon />
                  </Button>
                </div>
                <div className={css({ border: `1px solid ${theme.colors['input.border']}`, borderRadius: '2px' })}>
                  <ReactCodeMirror
                    value={JSONbigint.stringify(buildParamsForInstalledMode(parameters, payload), null, 2)}
                    readOnly
                    height="300px"
                    extensions={[StreamLanguage.define(json)]}
                    theme={editorTheme}
                  />
                </div>
              </div>
            </Tab>

            <Tab
              title="Code Snippet"
              key="code-snippet"
              overrides={{
                TabPanel: {
                  style: {
                    height: '100%',
                    ...expand({ padding: '12px 16px' }),
                  },
                },
              }}
            >
              {type === 'restpp endpoints' || query?.installed ? (
                <div className="flex flex-col gap-[12px]">
                  <div className="flex items-center gap-[8px] ">
                    <HttpMethodBadge apiType={method} />
                    <div
                      className={css({ ...theme.typography.Label, color: theme.colors['input.text'], fontWeight: 500 })}
                    >
                      Request URL
                    </div>
                    <div className="flex flex-1 items-center gap-[8px]">
                      <div className="flex-1">
                        <Input readOnly value={url} />
                      </div>
                      <Button
                        size="compact"
                        kind="text"
                        shape="square"
                        onClick={() => {
                          setCopyContent(url);
                        }}
                      >
                        <CopyIcon />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <TooltipLabel
                      label="Datebase Token"
                      tooltip="Generate a database token and paste it here so that you can copy the code snippets below to run the query in your own code."
                    />

                    <Button kind="secondary" size="compact" onClick={() => setIsCreateSecretOpen(true)}>
                      Generate Database Token
                    </Button>
                  </div>

                  <Input
                    placeholder="Enter your database secret here"
                    value={token}
                    onChange={(e) => setToken(e.currentTarget.value)}
                  />

                  <div>
                    <TooltipLabel label="Code Snippets" tooltip="" />
                    <SnippetsCopier payload={payload} url={url} secret={token} method={method} />
                  </div>
                </div>
              ) : (
                <div className={css({ color: theme.colors['text.secondary'] })}>
                  Code snippets are only available for installed queries.
                </div>
              )}
            </Tab>
          </Tabs>
        </div>
      </DrawerBody>
      <DrawerAction>
        <div className={css({ display: 'flex', gap: '8px', justifyContent: 'flex-end' })}>
          <QueryConfig canDoProfile={!!query?.installed && query.installMode !== 'UDF'} />
          <Button overrides={{ BaseButton: { style: { height: '30px' } } }} size="compact" onClick={onRunClick}>
            Run
          </Button>
        </div>
      </DrawerAction>
      <CreateToken isOpen={isCreateSecretOpen} onClose={() => setIsCreateSecretOpen(false)} workspace={wp} />
    </Drawer>
  );
}
