import { GLO<PERSON>L_VIEW, ID_TOKEN_KEY } from '@/contexts/workspaceContext';
import { requestGSQLCommand } from '@/pages/editor/result/api';
import { GSQL_COMMAND, parseGSQLMessage } from '@/utils/graphEditor';
import { eventStream } from '@/utils/graphEditor/sse-client';
import { getErrorMessage } from '@/utils/utils';
import { parseJSON, GsqlQueryMeta, QueryMetaLogic } from '@tigergraph/tools-models';
import { Profile } from '@tigergraph/tools-ui/esm/profile';
import EventEmitter from 'eventemitter3';
import JSONbig from 'json-bigint';
import { nanoid } from 'nanoid';
import { buildParamsForInstalledMode, buildParamsForInterpretedMode } from '@/utils/queryParam';
import { getInterpretQueryCode } from '@/pages/editor/query/util';
import { supportQueryProfile } from '@/utils/supportFeature';
import { compare } from 'compare-versions';

const JSON_START_CHARS = ['{', '['];
const JSON_END_CHARS = ['}', ']'];
const isJSONEnd = (ch: string, jsonStart: string) => {
  if (ch === '}') {
    return jsonStart === '{';
  }
  if (ch === ']') {
    return jsonStart === '[';
  }
  return false;
};

// We split the result of a GSQL script into chunks.
// There are 2 chunk types:
// 1. JSON chunk: the chunk contains a JSON object(Most of time it's a query result).
// We will display this type of chunk by table, JSON or graph in ResultChunk component.
// 2. Non-JSON chunk: the chunk is not a JSON object
export class ResultChunk {
  content: string = ''; // Raw content
  json: Record<string, any> | null = null; // Parsed JSON
  // The returned messages from GSQL client could contain commands like GSQL_MOVE_CURSOR_UP,
  // we need to keep track of the cursor position in the result.
  cursorIdx = 0;
  id = nanoid();
  // track the start char of a JSON chunk in the result.
  jsonStartCh: string = '';
  constructor() {}

  end() {
    try {
      this.json = parseJSON(this.content);
    } catch (error) {
      //
    }
  }

  isJSON() {
    return this.jsonStartCh || this.json;
  }
}

export class CommandResult {
  done = false;
  error = false;
  chunks: ResultChunk[] = [];
  profile: Profile | undefined = undefined;
  constructor() {}

  addNewChunk() {
    const chunk = new ResultChunk();
    this.chunks.push(chunk);
    return chunk;
  }
}

export class Command {
  id: string;
  type: string;
  workspace_id: string;
  graph: string;
  memoryLimit: number;
  timeLimit: number;

  constructor(type: string, workspace_id: string, graph: string, memoryLimit: number, timeLimit: number) {
    this.id = nanoid();
    this.type = type;
    this.workspace_id = workspace_id;
    this.graph = graph;
    this.memoryLimit = memoryLimit;
    this.timeLimit = timeLimit;
  }
}
export class GSQLCommand extends Command {
  GSQLCode: string;

  constructor(workspace_id: string, graph: string, memoryLimit: number, timeLimit: number, GSQLCode: string) {
    super('GSQL', workspace_id, graph, memoryLimit, timeLimit);
    this.GSQLCode = GSQLCode;
  }
}
export class QueryCommand extends Command {
  query: GsqlQueryMeta;
  queryPayload: Record<string, any>;
  runOptions: { memoryLimit: number; timeLimit: number; profile: boolean; token?: string };
  workspace: { nginx_host: string; tg_version: string };
  profile: boolean = false;

  constructor(
    workspace_id: string,
    graph: string,
    query: GsqlQueryMeta,
    queryPayload: Record<string, any>,
    runOptions: { memoryLimit: number; timeLimit: number; profile: boolean; token?: string },
    workspace: { nginx_host: string; tg_version: string }
  ) {
    super('Query', workspace_id, graph, runOptions.memoryLimit, runOptions.timeLimit);
    this.query = query;
    this.queryPayload = queryPayload;
    this.runOptions = runOptions;
    this.workspace = workspace;
  }
}

export type EventName = 'error' | 'progress' | 'finish';
export interface Event {
  target: string;
  payload?: CommandResult;
}

interface SSEEventPayload {
  error: boolean;
  done: boolean;
  message: string;
  results: string;
}

function isJSON(str: string) {
  try {
    const obj = JSON.parse(str);
    if (obj && typeof obj === 'object') {
      return true;
    }
  } catch (err) {
    return false;
  }
  return false;
}

class CommandExecutor extends EventEmitter {
  private cmdResults: Map<string, CommandResult> = new Map();

  constructor() {
    super();
  }

  getCmdResult(id: string) {
    return this.cmdResults.get(id);
  }

  removeCmdResult(id: string) {
    this.cmdResults.delete(id);
  }

  async executeGSQL(cmd: GSQLCommand, host: string, currentGraph: string, setCurrentGraph: (graph: string) => void) {
    if (this.getCmdResult(cmd.id)) {
      return;
    }

    const cmdResult = new CommandResult();
    this.cmdResults.set(cmd.id, cmdResult);

    const res = await requestGSQLCommand(cmd, host, currentGraph);
    if (res.status >= 300) {
      const errorMsg = (await res.text()) || res.statusText;
      this.handleError(cmd.id, errorMsg);
      this.handleFinish(cmd.id);
      return;
    }

    const events = eventStream(res.body);
    cmdResult.addNewChunk();
    for await (const event of events) {
      const payload = JSON.parse(event.data) as SSEEventPayload;
      if (payload.error) {
        this.handleError(cmd.id, payload.message);
        this.handleFinish(cmd.id);
        return;
      }

      const gsqlMessage = payload.results;
      if (gsqlMessage) {
        const parsedGSQLResult = parseGSQLMessage(gsqlMessage);

        const cookie = parsedGSQLResult.gsqlCookie;
        if (cookie) {
          if (cookie.graph) {
            setCurrentGraph(cookie.graph);
          } else {
            setCurrentGraph(GLOBAL_VIEW);
          }
        }

        if (parsedGSQLResult.message) {
          this.handleGSQLMsg(cmdResult, cmd.id, parsedGSQLResult.message);
        }

        if (parsedGSQLResult.errCode) {
          this.handleError(cmd.id);
        }
      }

      if (payload.done) {
        this.handleFinish(cmd.id);
      }
    }

    this.handleFinish(cmd.id);
  }

  private emitEvent(event: EventName, eventObj: Event) {
    super.emit(event, eventObj);
  }

  private handleFinish(cmdId: string) {
    let cmdResult = this.cmdResults.get(cmdId);
    if (!cmdResult) {
      return;
    }

    cmdResult.done = true;
    this.cmdResults.set(cmdId, cmdResult);
    this.emitEvent('finish', { target: cmdId, payload: cmdResult });
  }

  // Mark the result as error and save the error message
  private handleError(cmdId: string, errMsg?: string) {
    const cmdResult = this.cmdResults.get(cmdId);
    if (cmdResult) {
      cmdResult.error = true;
      if (errMsg) {
        const chunk = new ResultChunk();
        chunk.content = errMsg;
        cmdResult.chunks = [chunk];
      }

      this.cmdResults.set(cmdId, cmdResult);
      this.emitEvent('error', { target: cmdId, payload: cmdResult });
    }
  }

  private handleGSQLMsg(cmdResult: CommandResult, cmdId: string, msg: string) {
    let chunk = cmdResult.chunks[cmdResult.chunks.length - 1];
    // Handle the message line by line
    const lines = msg.split('\n');
    lines.forEach((line: string, lineIdx: number) => {
      // Split GSQL messages to chunks by JSON_START and JSON_END
      // There are two cases that we finish a chunk and create a new one
      // 1. when the new line is JSON_START, we assume there is a JSON object following
      // 2. when the previous line is JSON_END, we assume the JSON object ends here
      if (JSON_START_CHARS.includes(line)) {
        if (chunk.content) {
          chunk.end();
          chunk = cmdResult.addNewChunk();
          chunk.jsonStartCh = line;
        } else {
          chunk.jsonStartCh = line;
        }
      }
      if (lineIdx > 0 && isJSONEnd(lines[lineIdx - 1], chunk.jsonStartCh)) {
        if (chunk.content) {
          chunk.end();
          chunk = cmdResult.addNewChunk();
        }
      }

      if (lineIdx !== lines.length - 1) {
        // Append the new line character that was removed when calling split
        line += '\n';
      }

      // remove the color code
      // eslint-disable-next-line no-control-regex
      line = line.replace(/\u001b\[[0-9;]*m/g, '');
      if (line.startsWith(GSQL_COMMAND.MOVE_CURSOR_UP)) {
        let num = parseInt(line.split(',')[1]);
        let idx = chunk.cursorIdx;
        while (num && (idx = chunk.content.lastIndexOf('\n', idx - 1)) > -1) {
          --num;
        }
        idx = chunk.content.lastIndexOf('\n', idx - 1);
        chunk.cursorIdx = idx === -1 ? 0 : idx + 1;
      } else if (line.startsWith(GSQL_COMMAND.CLEAN_LINE)) {
        const newLineIdx = chunk.content.indexOf('\n', chunk.cursorIdx);
        if (newLineIdx > -1) {
          chunk.content = chunk.content.slice(0, chunk.cursorIdx) + chunk.content.slice(newLineIdx + 1);
        }
      } else if (line.indexOf('\r') > -1) {
        const lastCarriageIdx = line.lastIndexOf('\r');
        const lastLine = chunk.content.lastIndexOf('\n');
        chunk.content = chunk.content.slice(0, lastLine + 1) + line.slice(lastCarriageIdx + 1);
        chunk.cursorIdx = chunk.content.length;
      } else {
        chunk.content = chunk.content.slice(0, chunk.cursorIdx) + line + chunk.content.slice(chunk.cursorIdx);
        chunk.cursorIdx += line.length;
      }
    });

    if (isJSONEnd(lines[lines.length - 1], chunk.jsonStartCh)) {
      chunk.end();
      chunk = cmdResult.addNewChunk();
    }

    this.handleProgress(cmdId, cmdResult);
  }

  handleProgress(cmdId: string, cmdResult: CommandResult) {
    this.cmdResults.set(cmdId, cmdResult);
    this.emitEvent('progress', { payload: cmdResult, target: cmdId });
  }

  async executeQuery(cmd: QueryCommand) {
    if (this.getCmdResult(cmd.id)) {
      return;
    }

    const cmdResult = new CommandResult();
    this.cmdResults.set(cmd.id, cmdResult);

    // Construct URL and payload based on query type
    const queryParams = QueryMetaLogic.convertGSQLParameters(
      cmd.query.endpoint?.query?.[cmd.graph]?.[cmd.query.name]?.['GET/POST']?.parameters || {}
    );

    const enableProfile =
      supportQueryProfile({ tg_version: cmd.workspace.tg_version } as any) &&
      cmd.runOptions.profile &&
      cmd.query.installed &&
      cmd.query.installMode !== 'UDF';

    let URL = '';
    let payload: any;
    if (cmd.query.installed) {
      // For installed queries
      URL = `https://${cmd.workspace.nginx_host}/api/restpp/query/${cmd.graph}/${cmd.query.name}`;
      // if (cmd.runOptions.token) {
      //   // If use database token, skip gus proxy and access restpp endpoint directly
      //   URL = `https://${cmd.workspace.nginx_host}/restpp/query/${cmd.graph}/${cmd.query.name}`;
      // }
      payload = buildParamsForInstalledMode(queryParams, cmd.queryPayload);
    } else {
      // For interpreted queries
      const urlParams = buildParamsForInterpretedMode(cmd.graph, queryParams, cmd.queryPayload);
      const path = compare(cmd.workspace.tg_version, '4.0.x', '>')
        ? `/api/gsql-server/gsql/v1/queries/interpret`
        : `/api/gsql-server/interpreted_query`;
      URL = `https://${cmd.workspace.nginx_host}${path}?${urlParams}`;
      payload = getInterpretQueryCode(cmd.query);
    }

    let data = null;
    try {
      const commonHeaders: Record<string, string> = {};
      if (cmd.memoryLimit && Number(cmd.memoryLimit)) {
        commonHeaders['GSQL-QueryLocalMemLimitMB'] = `${cmd.memoryLimit}`;
      }
      if (cmd.timeLimit && Number(cmd.timeLimit)) {
        commonHeaders['Gsql-Timeout'] = `${Number(cmd.timeLimit) * 1000}`;
      }
      if (enableProfile) {
        commonHeaders['Profile'] = 'BASIC';
      }

      const token = (cmd.query.installed && cmd.runOptions.token) || sessionStorage.getItem(ID_TOKEN_KEY);
      const res = await fetch(URL, {
        method: 'POST',
        body: typeof payload === 'string' ? payload : JSONbig.stringify(payload),
        headers: {
          'Content-Type': 'text/plain',
          Authorization: `Bearer ${token}`,
          ...commonHeaders,
        },
      });
      const resAsText = await res.text();
      data = parseJSON(resAsText);
    } catch (error) {
      this.handleError(cmd.id, getErrorMessage(error as Error));
      return;
    }

    // Handle the profile data
    cmdResult.profile = data.profile;

    if (data.results) {
      const chunk = new ResultChunk();
      chunk.json = data.results;
      cmdResult.chunks = [chunk];
      this.handleProgress(cmd.id, cmdResult);
      this.handleFinish(cmd.id);
      return;
    }

    this.handleError(cmd.id, 'No result');
  }
}

const commandExecutor = new CommandExecutor();
export default commandExecutor;
