import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import React, { Key, useCallback } from 'react';
// import { Tab } from 'baseui/tabs-motion';
// import { Tabs } from '@tigergraph/app-ui-lib/tab';
import { Tab, Tabs } from '@/components/Tab';
import { ResultTab } from '@/pages/editor/result/ResultTab';
import { generateTabOverrides } from '@/utils/graphEditor';
import Close from '@/assets/close.svg';
import ResultIcon from '@/assets/result-icon.svg';
import ResultIconDark from '@/assets/result-icon-dark.png';
import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import IconButton from '@/components/IconButton';
import { MdKeyboardArrowLeft, MdKeyboardArrowRight, MdMoreVert } from 'react-icons/md';
import { useTheme } from '@/contexts/themeContext';
import { Command, CommandResult, GSQLCommand, QueryCommand } from '@/pages/editor/result/CommandExecutor';
import { expand } from 'inline-style-expand-shorthand';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TableBuilder } from '@tigergraph/app-ui-lib/table';
import { TableBuilderColumn } from 'baseui/table-semantic';
import StatefulPopover from '@/pages/editor/StatefulPopover';

interface NextOrPreviousCmdProps {
  direction: 'next' | 'previous';
  commandList: Command[];
  activeCmdId: Key;
  setActiveCmdId: (activeKey: Key) => void;
}

function NextOrPreviousCmd({ direction, commandList, activeCmdId, setActiveCmdId }: NextOrPreviousCmdProps) {
  const [css] = useStyletron();

  const previousDisabled = !commandList.length || commandList[0].id === activeCmdId;
  const nextDisabled = !commandList.length || commandList[commandList.length - 1].id === activeCmdId;

  const goToPrev = () => {
    const idx = commandList.findIndex((cmd) => cmd.id === activeCmdId);
    if (idx > 0) {
      setActiveCmdId(commandList[idx - 1].id);
    } else {
      setActiveCmdId(0);
    }
  };

  const goToNext = () => {
    if (activeCmdId === 0) {
      setActiveCmdId(commandList[0].id);
    } else {
      const idx = commandList.findIndex((cmd) => cmd.id === activeCmdId);
      if (idx < commandList.length - 1) {
        setActiveCmdId(commandList[idx + 1].id);
      }
    }
  };

  return (
    <div
      className={css({
        width: '16px',
        position: 'absolute',
        height: '32px',
        top: '5px',
        zIndex: 1,
        borderRadius: '0',
        ...(direction === 'next' ? { right: '0px' } : { left: '0px' }),
      })}
    >
      <IconButton
        className={css({
          width: '100%',
          height: '100%',
          display: (direction === 'next' ? nextDisabled : previousDisabled) ? 'none' : 'block',
        })}
        onClick={() => {
          direction === 'next' ? goToNext() : goToPrev();
        }}
      >
        {direction === 'next' ? (
          <MdKeyboardArrowRight title="next-command" />
        ) : (
          <MdKeyboardArrowLeft title="prev-command" />
        )}
      </IconButton>
    </div>
  );
}

interface ResultPanelProps {
  commandList: Command[];
  setCommandList: React.Dispatch<React.SetStateAction<Command[]>>;
  height: number;
  activeCmdId: Key;
  setActiveCmdId: (activeKey: Key) => void;
  onClickDeleteTab: (id: string) => void;
  isCommandRunning: boolean;
  currentRunningCommand: Command | null;
  handleFinish: (cmd: Command, result: CommandResult) => void;
}

export default function ResultPanel({
  commandList,
  setCommandList,
  height,
  activeCmdId,
  setActiveCmdId,
  onClickDeleteTab,
  isCommandRunning,
  currentRunningCommand,
  handleFinish,
}: ResultPanelProps) {
  const [css, theme] = useStyletron();
  const { themeType } = useTheme();

  const { currentWorkspace } = useWorkspaceContext();

  const onCommandChanged = useCallback(
    (command: Command) => {
      setCommandList((commandList) => {
        let result = commandList.map((c) => (c.id === command.id ? command : c));
        return result;
      });
    },
    [setCommandList]
  );

  const onCloseAllTabs = useCallback(() => {
    setCommandList([]);
    setActiveCmdId(0);
  }, [setCommandList, setActiveCmdId]);

  const onCloseOtherTabs = useCallback(
    (currentId: string) => {
      setCommandList((commandList) => {
        const currentCommand = commandList.find((cmd) => cmd.id === currentId);
        return currentCommand ? [currentCommand] : [];
      });
      setActiveCmdId(currentId);
    },
    [setCommandList, setActiveCmdId]
  );

  return (
    <div
      className={css({
        height: '100%',
        borderTop: `1px solid ${theme.colors.divider}`,
        position: 'relative',
      })}
    >
      {commandList.length === 0 ? (
        <div
          className={css({
            fontSize: '12px',
            textAlign: 'center',
            color: theme.colors['text.secondary'],
          })}
        >
          <img
            src={themeType === 'light' ? ResultIcon : ResultIconDark}
            style={{
              margin: '50px auto 16px',
            }}
          />
          {currentWorkspace ? 'Select a file and hit ⌘ + enter to run it.' : ''}
        </div>
      ) : (
        <>
          <NextOrPreviousCmd
            direction="previous"
            commandList={commandList}
            activeCmdId={activeCmdId}
            setActiveCmdId={setActiveCmdId}
          />
          <Tabs
            renderAll
            activeKey={activeCmdId}
            onChange={({ activeKey }) => {
              setActiveCmdId(activeKey);
            }}
            overrides={{
              Root: {
                style: () => ({
                  height: '100%',
                }),
              },
            }}
          >
            {commandList.map((command) => {
              return (
                <Tab
                  key={command.id}
                  title={
                    <ResultTabTitle
                      command={command}
                      currentRunningCommand={currentRunningCommand}
                      onClickDeleteTab={onClickDeleteTab}
                      isCommandRunning={isCommandRunning}
                      onCloseAllTabs={onCloseAllTabs}
                      onCloseOtherTabs={onCloseOtherTabs}
                    />
                  }
                  overrides={generateTabOverrides(height, theme)}
                >
                  <ResultTab command={command} onCommandChanged={onCommandChanged} onFinished={handleFinish} />
                </Tab>
              );
            })}
          </Tabs>
          <NextOrPreviousCmd
            direction="next"
            commandList={commandList}
            activeCmdId={activeCmdId}
            setActiveCmdId={setActiveCmdId}
          />
        </>
      )}
    </div>
  );
}

interface ResultTabTitleProops {
  command: Command;
  isCommandRunning: boolean;
  currentRunningCommand: Command | null;
  onClickDeleteTab: (id: string) => void;
  onCloseAllTabs: () => void;
  onCloseOtherTabs: (id: string) => void;
}

function ResultTabTitle({
  command: cmd,
  isCommandRunning,
  currentRunningCommand,
  onClickDeleteTab,
  onCloseAllTabs,
  onCloseOtherTabs,
}: ResultTabTitleProops) {
  const [css, theme] = useStyletron();

  const getTitle = () => {
    if (cmd.type === 'GSQL') {
      return (cmd as GSQLCommand).GSQLCode.slice(0, 100);
    }
    return `Run query ${(cmd as QueryCommand).query.name}`;
  };

  const getTooltipContent = () => {
    if (cmd.type === 'GSQL') {
      return (
        <pre
          style={{
            margin: 0,
            ...expand({ padding: 0 }),
            color: theme.colors['text.primary'],
            fontSize: '12px',
            maxWidth: '400px',
            overflow: 'auto',
          }}
        >
          {(cmd as GSQLCommand).GSQLCode}
        </pre>
      );
    }

    const queryCmd = cmd as QueryCommand;
    const hasPayload = Object.keys(queryCmd.queryPayload).length > 0;

    // Convert payload object to array format for TableBuilder
    const payloadData = hasPayload
      ? Object.entries(queryCmd.queryPayload).map(([key, value]) => ({
          parameter: key,
          value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        }))
      : [];

    return (
      <div>
        <div
          style={{
            fontWeight: 600,
            marginBottom: hasPayload ? '8px' : '0',
            fontSize: '14px',
            color: theme.colors['text.primary'],
          }}
        >
          {queryCmd.query.name}
        </div>
        {hasPayload && (
          <TableBuilder
            data={payloadData}
            overrides={{
              TableHeadCell: { style: { ...expand({ padding: '4px 8px' }) } },
              TableBodyCell: { style: { ...expand({ padding: '4px 8px' }) } },
            }}
          >
            <TableBuilderColumn header="Parameter">
              {(row: { parameter: string; value: string }) => (
                <div className={css({ color: theme.colors['text.primary'], fontSize: '12px' })}>{row.parameter}</div>
              )}
            </TableBuilderColumn>
            <TableBuilderColumn header="Value">
              {(row: { parameter: string; value: string }) => (
                <div
                  className={css({ color: theme.colors['text.primary'], fontSize: '12px', wordBreak: 'break-word' })}
                >
                  {row.value}
                </div>
              )}
            </TableBuilderColumn>
          </TableBuilder>
        )}
      </div>
    );
  };

  return (
    <StatefulPopover placement={'topLeft'} triggerType="hover" content={getTooltipContent} ignoreBoundary>
      <div
        className={css({
          height: '40px',
          lineHeight: '40px',
          width: '100%',
          display: 'flex',
          textAlign: 'left',
          alignItems: 'center',
          justifyContent: 'space-between',
          position: 'relative',
          paddingRight: '24px',
          boxSizing: 'border-box',
        })}
      >
        <div
          className={css({
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            maxWidth: '80%',
          })}
        >
          {getTitle()}
        </div>
        <div className={css({ position: 'absolute', right: '8px', display: 'flex', alignItems: 'center', gap: '4px' })}>
          {isCommandRunning && currentRunningCommand?.id === cmd.id ? (
            <Spinner title="Running" $color={theme.colors['icon.primary']} $size={'14px'} $borderWidth={'2px'} />
          ) : (
            <>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <IconButton title="More Options" type="button">
                    <MdMoreVert size={16} color={theme.colors['icon.primary']} />
                  </IconButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onSelect={() => onCloseAllTabs()}>Close All Tabs</DropdownMenuItem>
                  <DropdownMenuItem onSelect={() => onCloseOtherTabs(cmd.id)}>Close Other Tabs</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <img
                className={'close-icon'}
                onClick={(e) => {
                  onClickDeleteTab(cmd.id);
                  e.stopPropagation();
                }}
                src={Close}
                style={{ fontSize: '16px', cursor: 'pointer' }}
                alt="close"
              />
            </>
          )}
        </div>
        <div
          className={css({
            position: 'absolute',
            width: '1px',
            height: '18px',
            background: `${theme.colors.divider}`,
            right: 0,
            top: 'calc(50% - 9px)',
          })}
        ></div>
      </div>
    </StatefulPopover>
  );
}
