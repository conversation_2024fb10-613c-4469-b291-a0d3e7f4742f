import { useState } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { installAlgorithm } from './api';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { PLACEMENT } from 'baseui/popover';
import IconButton from '@/components/IconButton';
import { MdFileUpload } from 'react-icons/md';
import Popover from '@/pages/editor/Popover';
import clsx from 'clsx';
import { GraphSelector } from '@/pages/explore/explore/GraphSelector';
import { GLOBAL_GRAPH_NAME } from '@tigergraph/tools-models';
import { AlgorithmItem } from '@/pages/editor/scienceLibrary/useGraphAlgorithms';

interface InstallGDSPopoverProps {
  algorithm: AlgorithmItem;
}

export default function InstallGDSPopover({ algorithm }: InstallGDSPopoverProps) {
  const [css, theme] = useStyletron();
  const [isInstalling, setIsInstalling] = useState(false);
  const { currentGraph, graphNames, isFetchingSimpleAuth } = useWorkspaceContext();
  const [selectedGraph, setSelectedGraph] = useState(currentGraph !== GLOBAL_GRAPH_NAME ? currentGraph : '');
  const [isOpen, setIsOpen] = useState(false);

  const { algorithmName } = algorithm;

  const graphNamesExcludeGlobal = graphNames.filter((g) => g !== GLOBAL_GRAPH_NAME);

  // Reset state when popover opens
  const handleOpen = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      const resp = await installAlgorithm(selectedGraph, algorithmName!);
      if (resp.error) {
        throw new Error(resp.message);
      }
      if (resp.results?.failed?.length) {
        throw new Error(`Failed to install algorithm: ${resp.results.failed[0].errorMessage}`);
      }
      showToast({
        kind: 'positive',
        message: `Algorithm "${algorithmName}" installed successfully to graph ${selectedGraph}`,
      });
      // Close the popover after successful installation
      handleClose();
    } catch (error) {
      showToast({
        kind: 'negative',
        message: `Failed to install algorithm: ${(error as Error).message || 'Unknown error'}`,
      });
    } finally {
      setIsInstalling(false);
    }
  };

  const PopoverContent = () => (
    <div className={clsx('flex flex-col gap-[12px] w-[320px]', css({ color: theme.colors['text.primary'] }))}>
      <div
        className={css({
          fontSize: '14px',
          fontWeight: 600,
          lineHeight: '16px',
        })}
      >
        Install GDS
      </div>

      <div className={css({ display: 'flex', flexDirection: 'column', gap: '8px' })}>
        <div>
          <div className={css({ marginBottom: '8px', fontWeight: 500 })}>Query name</div>
          <div
            className={css({
              padding: '8px',
              border: `1px solid ${theme.colors['input.border']}`,
              borderRadius: '2px',
              backgroundColor: theme.colors['input.background'],
              ...theme.typography['Body2'],
            })}
          >
            {algorithmName}
          </div>
        </div>

        <div>
          <div className={css({ marginBottom: '8px', fontWeight: 500 })}>Select a graph to install</div>
          <div className={css({ width: '100%' })}>
            <GraphSelector
              graphs={graphNamesExcludeGlobal}
              graph={selectedGraph}
              onGraphChanged={(graphName) => {
                setSelectedGraph(graphName);
              }}
              isFetching={isFetchingSimpleAuth}
            />
          </div>
        </div>
      </div>

      <div className={css({ display: 'flex', justifyContent: 'flex-end', gap: '8px' })}>
        <Button kind="secondary" onClick={handleClose}>
          Cancel
        </Button>
        <Button onClick={handleInstall} isLoading={isInstalling} disabled={!selectedGraph}>
          Install
        </Button>
      </div>
    </div>
  );

  return (
    <div className="relative flex items-center ml-[-2px]">
      <Popover
        content={PopoverContent}
        placement={PLACEMENT.bottomLeft}
        accessibilityType="tooltip"
        ignoreBoundary
        showArrow={false}
        isOpen={isOpen}
        onClickOutside={handleClose}
      >
        <div
          className={css({
            position: 'absolute',
            left: 0,
            right: 0,
            bottom: 0,
          })}
        />
      </Popover>
      <IconButton title="Install" onClick={() => setIsOpen(true)}>
        <MdFileUpload size={18} color={theme.colors['icon.primary']} />
      </IconButton>
    </div>
  );
}
