import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ArrowDown, ArrowUp } from '@/pages/home/<USER>';
import { useEffect, useState } from 'react';
import IconButton from '@/components/IconButton.tsx';
import { Input } from '@tigergraph/app-ui-lib/input';
import ReactCodeMirror from '@uiw/react-codemirror';
import { xcodeDark, xcodeLight } from '@uiw/codemirror-theme-xcode';
import useCopyClipboard from 'react-use-clipboard';
import { showToast } from '@/components/styledToasterContainer';
import { ApiType, cmOptions } from '@/pages/workgroup/tab/restPP/type.ts';
import { ApiSetting } from '@/pages/workgroup/tab/restPP/ApiSetting.tsx';
import { generateCurl, generateJavascript, generatePython, Heading } from '@/pages/workgroup/tab/restPP/Util.tsx';
import { useMutation, useQuery } from 'react-query';
import { TableBuilderColumn } from 'baseui/table-semantic';
import { TableBuilder } from '@tigergraph/app-ui-lib/table';
import { Button } from '@tigergraph/app-ui-lib/button';
import { axiosCluster } from '@/lib/network.ts';
import { StreamLanguage } from '@codemirror/language';
import { json } from '@codemirror/legacy-modes/mode/javascript';
import { expand } from 'inline-style-expand-shorthand';
import { getErrorMessage } from '@/utils/utils.ts';
import { AxiosError } from 'axios';
import { ID_TOKEN_KEY } from '@/contexts/workspaceContext.tsx';
import { useTheme } from '@/contexts/themeContext';
import { MdContentCopy } from 'react-icons/md';
import { GsqlParameter, QueryMetaLogic, QueryParam } from '@tigergraph/tools-models';
import { buildParamsForInterpretedMode } from '@/utils/queryParam';
import HttpMethodBadge from '@/components/HttpMethodBadge';
import RunAPIDrawer from '@/pages/editor/query/RunAPIDrawer';
import { useWorkspaceContext } from '@/contexts/workspaceContext';

export interface ApiContainerProps {
  apiName: string;
  apiInfo: Record<string, GsqlParameter>;
  baseUrl: string;
  graphName: string;
}

export function ApiContainer({ apiName, apiInfo, baseUrl, graphName }: ApiContainerProps) {
  const [css, theme] = useStyletron();
  const { currentWorkspace } = useWorkspaceContext();
  const [pathParams, setPathParams] = useState<QueryParam[]>([]);
  const [queryParams, setQueryParams] = useState<QueryParam[]>([]);

  const [isRunDrawerOpen, setIsRunDrawerOpen] = useState(false);

  // set queryParams and pathParams
  useEffect(() => {
    const pattern = /{([^}]+)}/g;
    const matches = apiName.split(' ')[1].matchAll(pattern);
    let pathParams: QueryParam[] = [];
    for (const match of matches) {
      pathParams.push({
        paramName: match[1],
        paramType: { type: 'STRING' },
        paramDefaultValue: match[1] === 'graph_name' ? graphName : '',
      });
    }
    setPathParams(pathParams);

    // the parameters in apiInfo.parameters except path parameters are query parameters
    const filteredParams: Record<string, GsqlParameter> = {};
    if (apiInfo) {
      for (const param of Object.keys(apiInfo)) {
        if (!pathParams.find((p) => p.paramName === param)) {
          filteredParams[param] = apiInfo[param];
        }
      }
      const queryParams = QueryMetaLogic.convertGSQLParameters(filteredParams);
      const graphNameParam = queryParams.find((p) => p.paramName === 'graph_name');
      if (graphNameParam) {
        graphNameParam.paramDefaultValue = graphName;
      }

      setQueryParams(queryParams);
    }
  }, [apiInfo, apiName, graphName]);

  const apiType: ApiType = apiName.split(' ')[0] as ApiType;
  const apiPath = apiName.split(' ')[1];

  const { themeType } = useTheme();

  const backgroundColorMap = {
    [ApiType.GET]: themeType === 'light' ? 'rgba(37, 131, 222, 0.10)' : 'rgba(37, 131, 222, 0.10)',
    [ApiType.POST]: themeType === 'light' ? 'rgba(39, 186, 63, 0.10)' : 'rgba(39, 186, 63, 0.10)',
    [ApiType.PUT]: themeType === 'light' ? 'rgba(248, 173, 104, 0.15)' : 'rgba(248, 173, 104, 0.15)',
    [ApiType.DELETE]: themeType === 'light' ? 'rgba(214, 69, 69, 0.10)' : 'rgba(214, 69, 69, 0.10)',
  };

  const colorMap = {
    [ApiType.GET]: theme.colors['background.informative.subtle'],
    [ApiType.POST]: theme.colors['background.success.bold'],
    [ApiType.PUT]: theme.colors['background.brand.bold'],
    [ApiType.DELETE]: theme.colors['background.danger.subtle'],
  };

  return (
    <div
      className={css({
        backgroundColor: backgroundColorMap[apiType],
        marginBottom: '10px',
        padding: '8px',
        borderRadius: '2px',
        ...expand({
          border: `1px solid ${colorMap[apiType]}`,
        }),
      })}
    >
      <div
        key={apiName}
        className={css({
          display: 'flex',
          justifyContent: 'space-between',
          height: '24px',
          alignItems: 'center',
        })}
      >
        <div
          onClick={() => setIsRunDrawerOpen(true)}
          className={css({
            display: 'flex',
            alignItems: 'center',
            maxWidth: '95%',
            gap: '8px',
          })}
        >
          <HttpMethodBadge apiType={apiType} />
          <div
            className={css({
              maxWidth: '100%',
              overflow: 'auto',
            })}
          >
            {apiName.split(' ')[1]}
          </div>
        </div>
        <RunAPIDrawer
          isOpen={isRunDrawerOpen}
          onClose={() => setIsRunDrawerOpen(false)}
          type="restpp endpoints"
          wp={currentWorkspace!}
          graphName={graphName}
          path={`/restpp${apiPath}`}
          method={apiType}
          pathParameters={pathParams}
          parameters={queryParams}
        />
      </div>
    </div>
  );
}
