import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ApiType } from '../pages/workgroup/tab/restPP/type';

export default function HttpMethodBadge({ apiType }: { apiType: ApiType }) {
  const [css, theme] = useStyletron();
  const backgroundColorMap = {
    [ApiType.GET]: theme.colors['background.informative.subtle'],
    [ApiType.POST]: theme.colors['background.success.bold'],
    [ApiType.PUT]: theme.colors['background.brand.bold'],
    [ApiType.DELETE]: theme.colors['background.danger.subtle'],
  };

  return (
    <div
      className={css({
        backgroundColor: backgroundColorMap[apiType],
        display: 'inline-flex',
        justifyContent: 'space-around',
        borderRadius: '2px',
        padding: '4px 12px',
        minWidth: '73px',
        color: theme.colors['text.inverse'],
        ...theme.typography.Body2,
      })}
    >
      {apiType}
    </div>
  );
}
