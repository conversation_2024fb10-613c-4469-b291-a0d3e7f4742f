import {
  QueryParam,
  QueryParamListType,
  QueryParamMapType,
  QueryParamType,
  QueryParamVertexType,
} from '@tigergraph/tools-models';
import { format } from 'date-fns';
import { cloneDeep } from 'lodash-es';

export type ParamError = string | string[] | { key: string; value: string }[];
export type ParamErrors = Record<string, ParamError>;

enum IntegerTypes {
  INT = 'INT',
  UINT = 'UINT',
}

enum FloatingPointTypes {
  FLOAT = 'FLOAT',
  DOUBLE = 'DOUBLE',
}

const convertParameter = (type: string, val: unknown) => {
  if (type in FloatingPointTypes) {
    return Number(val);
  } else if (type in IntegerTypes) {
    return BigInt(val as string);
  } else {
    return val;
  }
};

export function getQueryDefalutPayloadByParams(params: QueryParam[]) {
  const payload: { [key: string]: any } = {};

  params.forEach((param) => {
    if (param.paramType.type === 'LIST') {
      payload[param.paramName] = [];
      return;
    }

    if (param.paramType.type === 'MAP') {
      payload[param.paramName] = [];
      return;
    }

    payload[param.paramName] = getSimpleTypeDefaultValue(param.paramType, param.paramDefaultValue);
  });

  // return JSONbigint.stringify(json, null, 2);
  return payload;
}

export function getSimpleTypeDefaultValue(paramType: QueryParamType, defaultValue?: any): any {
  switch (paramType.type) {
    case 'BOOL':
      return typeof defaultValue !== 'undefined' && defaultValue === 'true';
    case 'DATETIME':
      return typeof defaultValue !== 'undefined' ? defaultValue : format(new Date(), 'yyyy-MM-dd HH:mm:ss');
    case 'VERTEX':
      return {
        id: '',
        type:
          (paramType as QueryParamVertexType).vertexType !== '*' ? (paramType as QueryParamVertexType).vertexType : '',
      };
    default:
      return typeof defaultValue !== 'undefined' ? defaultValue : '';
  }
}

export function buildParamsForInterpretedMode(
  graphName: string,
  queryParams: QueryParam[],
  payload: Record<string, any>
) {
  let urlParams = new URLSearchParams();

  queryParams.forEach((queryParam) => {
    const paramName = queryParam.paramName;
    if (paramName in payload) {
      switch (queryParam.paramType.type) {
        case 'VERTEX': {
          /**
           * When run query in interpreted mode, "[0]" should be suffix of the parameter name
           * and type. Vertex type should be added.
           * For example:
           *   localhost:14240/gsqlserver/interpreted_query?a[0]=A&a[0].type=Person
           */
          urlParams.set(`${paramName}[0]`, payload[paramName].id);
          urlParams.set(paramName + '[0].type', payload[paramName].type);
          break;
        }
        case 'LIST': {
          const elementType = (<QueryParamListType>queryParam.paramType).elementType;
          const paramValue = payload[paramName] as any[];

          paramValue.forEach((param, index) => {
            if (elementType.type === 'VERTEX') {
              // If it is vertex without type, will push paramName[index] and paramName[index].type
              urlParams.set(paramName + `[${index}]`, param.id);
              urlParams.set(paramName + `[${index}].type`, param.type);
            } else {
              urlParams.append(paramName, param);
            }
          });
          break;
        }
        default:
          urlParams.set(paramName, payload[paramName]);
          break;
      }
    }
  });

  return urlParams.toString();
}

export function buildParamsForInstalledMode(queryParams: QueryParam[], params: any): any {
  const paramsPayload: Record<string, any> = cloneDeep(params);
  queryParams.forEach((queryParam) => {
    const param = params[queryParam.paramName];
    switch (queryParam.paramType.type) {
      case 'STRING':
      case 'BOOL':
      case 'DATETIME':
      case 'VERTEX': {
        break;
      }
      case 'INT':
      case 'UINT': {
        const integer = BigInt(param);
        paramsPayload[queryParam.paramName] = integer;
        break;
      }
      case 'FLOAT':
      case 'DOUBLE': {
        paramsPayload[queryParam.paramName] = +param;
        break;
      }
      case 'LIST': {
        const elementType = (queryParam.paramType as QueryParamListType).elementType;
        paramsPayload[queryParam.paramName] = param.map((item: any) => convertParameter(elementType.type, item));
        break;
      }
      case 'MAP': {
        const mapType = queryParam.paramType as QueryParamMapType;
        const keyType = mapType.keyType;
        const valueType = mapType.valueType;
        paramsPayload[queryParam.paramName] = {
          keylist: (param || []).map((item: { key: string; value: string }) =>
            convertParameter(keyType.type, item.key)
          ),
          valuelist: (param || []).map((item: { key: string; value: string }) =>
            convertParameter(valueType.type, item.value)
          ),
        };
        break;
      }
      default:
      // Unsupported types: Composite key vertex, Edge, Map, Json object.
    }
  });
  return paramsPayload;
}

export const getParamTypeDisplayName = (paramType: QueryParamType): string => {
  if (paramType.type === 'VERTEX') {
    const vertexType = (paramType as QueryParamVertexType).vertexType;
    return `VERTEX<${vertexType === '*' ? 'ANY' : vertexType}>`;
  }
  if (paramType.type === 'LIST') {
    const listItemType = (paramType as QueryParamListType).elementType;
    return `LIST<${getParamTypeDisplayName(listItemType)}>`;
  }
  if (paramType.type === 'MAP') {
    const mapType = paramType as QueryParamMapType;
    return `MAP<${getParamTypeDisplayName(mapType.keyType)}, ${getParamTypeDisplayName(mapType.valueType)}>`;
  }
  return paramType.type;
};
