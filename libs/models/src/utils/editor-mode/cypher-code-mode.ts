import * as Code<PERSON><PERSON>ror from 'codemirror';

const functions = ["ABS", "ACOS", "ALLSHORTESTPATHS", "ASIN", "ATAN", "ATAN2", "AVG", "CEIL", "COALESCE", "COLLECT", "COS", "COT", "COUNT", "DEGREES", "E", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EXP", "EXTRACT", "FILTER", "FLOOR", "HAVERSI<PERSON>", "HEAD", "ID", "KEYS", "LABELS", "LAST", "LEFT", "LENGTH", "LOG", "LOG10", "LOWER", "LTRIM", "MAX", "MIN", "NODE", "NODES", "PERCENTILECONT", "PERCENTILEDISC", "PI", "RADIANS", "RAND", "RANGE", "REDUCE", "<PERSON>EL", "REL<PERSON>IONSHIP", "REL<PERSON>IONSHIPS", "REPLA<PERSON>", "<PERSON>E<PERSON><PERSON><PERSON>", "RIGHT", "ROUND", "<PERSON><PERSON><PERSON>", "SHORTESTPATH", "<PERSON>IGN", "SIN", "<PERSON>IZ<PERSON>", "<PERSON>L<PERSON>", "SQRT", "STARTNODE", "STDEV", "STDEVP", "STR", "SUBSTRING", "SUM", "TAIL", "TAN", "TIMESTAMP", "TOFLOAT", "TOINT", "TOSTRING", "TRIM", "TYPE", "UPPER"];
const preds = ["ALL", "AND", "ANY", "CONTAINS", "EXISTS", "HAS", "IN", "NONE", "NOT", "OR", "SINGLE", "XOR"];
const keywords = ["AS", "ASC", "ASCENDING", "ASSERT", "BY", "CASE", "COMMIT", "CONSTRAINT", "CREATE", "CSV", "OPENCYPHER", "QUERY", "CYPHER", "DELETE", "DESC", "DESCENDING", "DETACH", "DISTINCT", "DROP", "ELSE", "END", "ENDS", "EXPLAIN", "FALSE", "FIELDTERMINATOR", "FOREACH", "FROM", "HEADERS", "IN", "INDEX", "IS", "JOIN", "LIMIT", "LOAD", "MATCH", "MERGE", "NULL", "ON", "OPTIONAL", "ORDER", "PERIODIC", "PROFILE", "REMOVE", "RETURN", "SCAN", "SET", "SKIP", "START", "STARTS", "THEN", "TRUE", "UNION", "UNIQUE", "UNWIND", "USING", "WHEN", "WHERE", "WITH", "CALL", "YIELD", "DISTRIBUTED"];
const systemKeywords = ["ACCESS", "ACTIVE", "ASSIGN", "ALL", "ALTER", "AS", "CATALOG", "CHANGE", "COPY", "CREATE", "CONSTRAINT", "CONSTRAINTS", "CURRENT", "DATABASE", "DATABASES", "DBMS", "DEFAULT", "DENY", "DROP", "ELEMENT", "ELEMENTS", "EXISTS", "FROM", "GRANT", "GRAPH", "GRAPHS", "IF", "INDEX", "INDEXES", "LABEL", "LABELS", "MANAGEMENT", "MATCH", "NAME", "NAMES", "NEW", "NODE", "NODES", "NOT", "OF", "ON", "OR", "PASSWORD", "POPULATED", "PRIVILEGES", "PROPERTY", "READ", "RELATIONSHIP", "RELATIONSHIPS", "REMOVE", "REPLACE", "REQUIRED", "REVOKE", "ROLE", "ROLES", "SET", "SHOW", "START", "STATUS", "STOP", "SUSPENDED", "TO", "TRAVERSE", "TYPE", "TYPES", "USER", "USERS", "WITH", "WRITE"];

export const cypherDictionary = [].concat(functions, preds, keywords, systemKeywords);

function wordRegexp(words) {
  return new RegExp("^(?:" + words.join("|") + ")$", "i");
};

export function defineCypherMode(cm: any) {
  cm.defineMode("cypher", function (config) {
    let indentUnit = config.indentUnit;
    let curPunc;
    let funcRegexp = wordRegexp(functions);
    let predRegexp = wordRegexp(preds);
    let keywordRegexp = wordRegexp(keywords);
    let systemKeywordRegexp = wordRegexp(systemKeywords);
    let operatorChars = /[*+\-<>=&|~%^]/;

    let tokenBase = function (stream, state) {
      curPunc = null
      let ch = stream.next();
      if (ch === '"') {
        stream.match(/^[^"]*"/);
        return "string";
      }
      if (ch === "'") {
        stream.match(/^[^']*'/);
        return "string";
      }
      if (/[{}\(\),\.;\[\]]/.test(ch)) {
        curPunc = ch;
        return "node";
      } else if (ch === "/" && stream.eat("/")) {
        stream.skipToEnd();
        return "comment";
      }
      if (ch == "/" && stream.eat("*")) {
        state.tokenize = tokenComment;
        return tokenComment(stream, state);
      }
      if (operatorChars.test(ch)) {
        stream.eatWhile(operatorChars);
        return null;
      } else {
        stream.eatWhile(/[_\w\d]/);
        if (stream.eat(":")) {
          stream.eatWhile(/[\w\d_\-]/);
          return "atom";
        }
        let word = stream.current();
        if (funcRegexp.test(word)) return "builtin";
        if (predRegexp.test(word)) return "def";
        if (keywordRegexp.test(word) || systemKeywordRegexp.test(word)) return "keyword";
        return "variable";
      }
    };
    function pushContext(state, type, col) {
      return state.context = {
        prev: state.context,
        indent: state.indent,
        col: col,
        type: type
      };
    };
    function popContext(state) {
      state.indent = state.context.indent;
      return state.context = state.context.prev;
    };
    function tokenComment(stream, state) {
      var maybeEnd = false, ch;
      while (ch = stream.next()) {
        if (ch == "/" && maybeEnd) {
          state.tokenize = tokenBase;
          break;
        }
        maybeEnd = (ch == "*");
      }
      return "comment";
    }

    return {
      startState: function (/*base*/) {
        return {
          tokenize: tokenBase,
          context: null,
          indent: 0,
          col: 0,
        };
      },
      token: function (stream, state) {
        if (stream.sol()) {
          if (state.context && (state.context.align == null)) {
            state.context.align = false;
          }
          state.indent = stream.indentation();
        }
        if (stream.eatSpace()) {
          return null;
        }
        let style = state.tokenize(stream, state);
        if (style !== "comment" && state.context && (state.context.align == null) && state.context.type !== "pattern") {
          state.context.align = true;
        }
        if (curPunc === "(") {
          pushContext(state, ")", stream.column());
        } else if (curPunc === "[") {
          pushContext(state, "]", stream.column());
        } else if (curPunc === "{") {
          pushContext(state, "}", stream.column());
        } else if (/[\]\}\)]/.test(curPunc)) {
          while (state.context && state.context.type === "pattern") {
            popContext(state);
          }
          if (state.context && curPunc === state.context.type) {
            popContext(state);
          }
        } else if (curPunc === "." && state.context && state.context.type === "pattern") {
          popContext(state);
        } else if (/atom|string|variable/.test(style) && state.context) {
          if (/[\}\]]/.test(state.context.type)) {
            pushContext(state, "pattern", stream.column());
          } else if (state.context.type === "pattern" && !state.context.align) {
            state.context.align = true;
            state.context.col = stream.column();
          }
        }
        return style;
      },
      indent: function (state, textAfter) {
        let firstChar = textAfter && textAfter.charAt(0);
        let context = state.context;
        if (/[\]\}]/.test(firstChar)) {
          while (context && context.type === "pattern") {
            context = context.prev;
          }
        }
        let closing = context && firstChar === context.type;
        if (!context) return 0;
        if (context.type === "keywords") return CodeMirror.commands.newlineAndIndent;
        if (context.align) return context.col + (closing ? 0 : 1);
        return context.indent + (closing ? 0 : indentUnit);
      }
    };
  });

  cm.modeExtensions["cypher"] = {
    autoFormatLineBreaks: function (text) {
      let i, lines, reProcessedPortion;
      lines = text.split("\n");
      reProcessedPortion = /\s+\b(return|where|order by|match|with|skip|limit|create|delete|set)\b\s/g;
      for (i = 0; i < lines.length; i++)
        lines[i] = lines[i].replace(reProcessedPortion, " \n$1 ").trim();
      return lines.join("\n");
    }
  };

  cm.defineMIME("application/x-cypher-query", "cypher");

}